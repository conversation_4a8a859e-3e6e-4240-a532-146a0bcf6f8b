import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:smsautoforwardapp/bnb.dart';
import 'package:smsautoforwardapp/login.dart';
import 'package:smsautoforwardapp/model/forward_all_email_model.dart';
import 'package:smsautoforwardapp/model/forward_all_url_model.dart';
import 'package:smsautoforwardapp/model/subscription_model.dart';
import 'package:url_launcher/url_launcher.dart';
import '../background_service.dart';
import '../model/user_model.dart';
import '../style.dart';

class AuthController extends GetxController {
  static AuthController instance = Get.find();
  Rx<bool> isAuthUpdating = false.obs;
  Rx<int> endTime = 0.obs;
  RxBool hasRestriction = false.obs;

  late Rx<User?> _user;
  bool isLoging = false;
  User? get user => _user.value;
  final _auth = FirebaseAuth.instance;
  RxList<UserModel> allUsersList = <UserModel>[].obs;

  Rx<int> isObscure = 1.obs;

  @override
  void onReady() {
    super.onReady();
    _user = Rx<User?>(_auth.currentUser);
    _user.bindStream(_auth.authStateChanges());
    ever(_user, loginRedirect);
  }

  @override
  void onClose() {
    super.onReady();
  }

  loginRedirect(var user) async {
    Timer(Duration(seconds: isLoging ? 0 : 2), () {
      if (_auth.currentUser == null) {
        isLoging = false;
        Get.offAll(() => const Login());
      } else {
        isLoging = true;
        sendAuthStateToAndroid(_auth.currentUser!.uid);
        Get.offAll(() => const BNB());
      }
    });
  }

  void googleLogin() async {
    String errorMessage = '';
    final GoogleSignIn googleSignIn = GoogleSignIn();
    isAuthUpdating.value = true;
    isLoging = true;

    googleSignIn.disconnect();

    try {
      final GoogleSignInAccount? googleSignInAccount =
          await googleSignIn.signIn();
      if (googleSignInAccount != null) {
        final GoogleSignInAuthentication googleAuth =
            await googleSignInAccount.authentication;
        final crendentials = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        final UserCredential userCredential =
            await _auth.signInWithCredential(crendentials);

        User? registereduser = userCredential.user;

        UserModel user = UserModel(
            name: registereduser!.displayName ?? '',
            email: registereduser.email,
            stripeCustomerID: '',
            noOfForwardsused: 0,
            noOfForwardsPerMonth: 50,
            createdAt: Timestamp.fromMillisecondsSinceEpoch(
                DateTime.now().millisecondsSinceEpoch),
            uid: registereduser.uid,
            isPremium: false,
            deviceCount: 0, // Default to 0 for new users
            currentSubscription: SubsriptionModel(
                subscriptionItem: '',
                subscriptionID: '',
                subscriptionPlan: '',
                planDescription: ''),
            forwardAllEmail:
                ForwaredAllEmailModelClass(isActive: false, recipients: ['']),
            forwardAllUrl: ForwaredAllURLModelClass(
                isActive: false, method: '', url: '', jsonBody: ''));

        final userDocRef =
            firestore.collection('users').doc(registereduser.uid);
        if (!(await userDocRef.get()).exists) {
          await firestore
              .collection('users')
              .doc(registereduser.uid)
              .set(user.toJson());
        }

        // Check subscription plan and device count
        final userDoc =
            await firestore.collection('users').doc(registereduser.uid).get();
        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          final subscriptionPlan =
              userData['currentSubscription']?['subscriptionPlan'] ?? '';
          final currentDeviceCount = userData['deviceCount'] ?? 0;

          if (subscriptionPlan != 'Elite Plan' && currentDeviceCount >= 1) {
            await stopBackgroundService();
            hasRestriction.value = true;
          }
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userDoc.id)
              .set({'deviceCount': currentDeviceCount + 1},
                  SetOptions(merge: true));
        }

        getSuccessSnackBar("successfully logged in");
      }
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "account-exists-with-different-credential":
          errorMessage =
              "An account already exists with the same email address but different sign-in credentials.";
          break;
        case "invalid-email":
          errorMessage = "Invalid email";
          break;
        case "network-request-failed":
          errorMessage = "There is no Internet connection";
          break;
        case "email-already-in-use":
          errorMessage = "The account already exists for that email.";
          break;
        case "user-disabled":
          errorMessage = "User is currently disabled";
          break;
        case "user-not-found":
          errorMessage = "User not found";
          break;
        case "wrong-password":
          errorMessage = "Wrong password";
          break;
        default:
          errorMessage = "Login Failed!";
          break;
      }
      getErrorSnackBar(errorMessage);
    } finally {
      isAuthUpdating.value = false;
    }
  }

  updateUserSubscription({
    required bool isPremiumUser,
    required int noOfForwardsPerMonth,
    required SubsriptionModel subsriptionModel,
  }) async {
    // Use temp user ID if provided, otherwise use current user ID
    final userId = user!.uid;

    Map<String, dynamic> updateData = {
      'isPremium': isPremiumUser,
      'noOfForwardsPerMonth': noOfForwardsPerMonth,
      'currentSubscription': subsriptionModel.toJson()
    };

    await firestore.collection('users').doc(userId).update(updateData);

    // If upgraded to Elite Plan, remove restrictions and start background service
    if (subsriptionModel.subscriptionPlan == 'Elite Plan') {
      hasRestriction.value = false;
      await sendAuthStateToAndroid(userId);
    }
  }

  updateSubscriptionEndsAt({required DateTime? subscriptionEndsAt}) async {
    await firestore.collection('users').doc(user!.uid).update({
      'subscriptionEndsAt': subscriptionEndsAt != null
          ? Timestamp.fromDate(subscriptionEndsAt)
          : null,
    });
  }

  updateStripeCustomerId({
    required String id,
  }) async {
    await firestore.collection('users').doc(user!.uid).update({
      'stripeCustomerID': id,
    });
  }

  signOut() async {
    final user = FirebaseAuth.instance.currentUser;

    if (user != null) {
      // Get current device count and decrement it
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final currentDeviceCount = userData['deviceCount'] ?? 0;
        final newDeviceCount =
            currentDeviceCount > 0 ? currentDeviceCount - 1 : 0;

        // Update device count and clear FCM token before sign out
        await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
          'deviceCount': newDeviceCount,
          'fcmToken': null,
        }, SetOptions(merge: true));
      }
    }

    await stopService();
  }

  stopService() async {
    await stopBackgroundService();

    await _auth.signOut();

    await Get.deleteAll();
  }

  // Method to check subscription expiration as backup to Stripe webhooks
  Future<void> checkSubscriptionExpiration() async {
    if (_auth.currentUser == null) return;

    try {
      final userDoc =
          await firestore.collection('users').doc(_auth.currentUser!.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final subscriptionEndsAt = userData['subscriptionEndsAt'] != null
            ? (userData['subscriptionEndsAt'] as Timestamp).toDate()
            : null;

        // Check if subscription has expired
        if (subscriptionEndsAt != null &&
            DateTime.now().isAfter(subscriptionEndsAt)) {
          // Update Firestore to revoke premium status
          await firestore.collection('users').doc(_auth.currentUser!.uid).set({
            'isPremium': false,
            'subscriptionEndsAt': null,
            'currentSubscription': {
              'subscriptionItem': '',
              'subscriptionID': '',
              'subscriptionPlan': '',
              'planDescription': ''
            },
            'noOfForwardsPerMonth': 50, // Reset to free tier limit
          }, SetOptions(merge: true));
        }
      }
    } catch (e) {
      //
    }
  }

  // Method to check device restrictions when landing on BNB
  Future<void> checkDeviceRestrictions() async {
    if (_auth.currentUser == null) return;

    try {
      final userDoc =
          await firestore.collection('users').doc(_auth.currentUser!.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final subscriptionPlan =
            userData['currentSubscription']?['subscriptionPlan'] ?? '';
        final currentDeviceCount = userData['deviceCount'] ?? 0;

        if (subscriptionPlan != 'Elite Plan' && currentDeviceCount > 1) {
          await stopBackgroundService();
          hasRestriction.value = true;
        } else {
          hasRestriction.value = false;
        }
      }
    } catch (e) {
      //
    }
  }

  void checkForUpdate() async {
    final info = await PackageInfo.fromPlatform();
    final currentVersion = info.version;

    final doc = await FirebaseFirestore.instance
        .collection('config')
        .doc('version')
        .get();
    final latestVersion = doc['latestVersion'];
    final forceUpdate = doc['forceUpdate'];
    final updateUrl = doc['updateUrl'];
    final updateDialogText = doc['updateDialogText'];

    if (currentVersion != latestVersion && forceUpdate == true) {
      // Only sign out if user is currently logged in
      if (_auth.currentUser != null) {
        await signOut();
        await Future.delayed(const Duration(seconds: 3));
      }
      Get.dialog(
          barrierDismissible: false,
          PopScope(
            canPop: false,
            child: AlertDialog(
              title: const Text('Update Required'),
              content: Text(updateDialogText),
              actions: [
                ElevatedButton(
                  child: const Text(
                    'Update Now',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: () {
                    launchUrl(Uri.parse(updateUrl));
                  },
                ),
              ],
            ),
          ));
    }
  }
}
